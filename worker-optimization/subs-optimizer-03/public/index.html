<!doctype html>
<html lang="en">
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>Hello, World!</title>
	</head>
	<body>
		<h1 id="heading"></h1>
		<p>This page comes from a static asset stored at `public/index.html` as configured in `wrangler.jsonc`.</p>
		<button id="button" type="button">Fetch a random UUID</button>
		<output id="random" for="button"></output>
		<script>
			fetch('/message')
				.then((resp) => resp.text())
				.then((text) => {
					const h1 = document.getElementById('heading');
					h1.textContent = text;
				});

			const button = document.getElementById("button");
			button.addEventListener("click", () => {
				fetch('/random')
					.then((resp) => resp.text())
					.then((text) => {
						const random = document.getElementById('random');
						random.textContent = text;
					});
			});
		</script>
	</body>
</html>
